using ClosedXML.Excel;
using iTextSharp.text;
using iTextSharp.text.pdf;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Drawing.Printing;
using System.IO;
using System.Web;
using System.Web.Script.Serialization;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Xml.Linq;

public partial class Analyze_recowish01 : System.Web.UI.Page
{
    dbconnection db = new dbconnection();
    App_Func appfun = new App_Func();

    protected void Page_Load(object sender, EventArgs e)
    {
        if (CheckUserSession() == false) { return; }
        if (!IsPostBack)
        {
            // 初始化頁面
        }
    }

    // 添加缺少的 RadioButtonList1_SelectedIndexChanged 方法
    protected void RadioButtonList1_SelectedIndexChanged(object sender, EventArgs e)
    {
        // 當選擇變更時，可以在這裡處理邏輯
        // 目前保持空白，或者可以加入一些 UI 更新邏輯
    }

    // 添加缺少的 RadioButtonList2_SelectedIndexChanged 方法
    protected void RadioButtonList2_SelectedIndexChanged(object sender, EventArgs e)
    {
        // 當查詢項目類型選擇變更時，可以在這裡處理邏輯
        // 目前保持空白，或者可以加入一些 UI 更新邏輯
    }

    //protected void Button1_Click(object sender, EventArgs e)
    //{
    //    // 顯示載入動畫
    //    ScriptManager.RegisterStartupScript(this, GetType(), "showLoading", "showLoading();", true);

    //    // 重置消息和圖表區域
    //    msg.Text = "";
    //    P1.Visible = false;

    //    try
    //    {
    //        GetGV1Data1();
    //    }
    //    catch (Exception ex)
    //    {
    //        msg.Text = "查詢時發生錯誤: " + ex.Message;
    //        ScriptManager.RegisterStartupScript(this, GetType(), "hideLoadingError", "hideLoading();", true);
    //    }
    //}

    // 添加缺少的 Button_search_Click 方法（與 Button1_Click 功能相同）
    protected void Button_search_Click(object sender, EventArgs e)
    {
        // 立即顯示按鈕被點擊的訊息
        msg.Text = "<p>🔄 查詢按鈕已被點擊！時間: " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "</p>";

        try
        {
            // 檢查用戶會話
            if (!CheckUserSession())
            {
                msg.Text += "<p style='color: red;'>❌ 用戶會話檢查失敗</p>";
                return;
            }

            msg.Text += "<p>✅ 用戶會話檢查通過</p>";

            // 基本驗證
            if (string.IsNullOrEmpty(RadioButtonList1.SelectedValue))
            {
                msg.Text += "<p style='color: orange;'>⚠️ 請選擇人數分析項目</p>";
                return;
            }

            if (string.IsNullOrEmpty(RadioButtonList2.SelectedValue))
            {
                msg.Text += "<p style='color: orange;'>⚠️ 請選擇查詢項目類型</p>";
                return;
            }

            msg.Text += $"<p>✅ 驗證通過 - 分析項目: {RadioButtonList1.SelectedValue}, 查詢類型: {RadioButtonList2.SelectedValue}</p>";

            // 重置圖表區域
            P1.Visible = false;

            msg.Text += "<p>🔍 開始執行查詢...</p>";
            GetGV1Data1();
        }
        catch (Exception ex)
        {
            msg.Text = $"<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>❌ 查詢時發生錯誤: {ex.Message}</div>";
            ScriptManager.RegisterStartupScript(this, GetType(), "hideLoadingError", $"hideLoading(); addDebugInfo('❌ 後端異常: {ex.Message.Replace("'", "\\'")}');", true);
        }
    }

    // 添加缺少的 GridView1_RowDataBound 方法
    protected void GridView1_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            // 可以在這裡添加行資料綁定的自定義邏輯
            // 例如：格式化數據、添加樣式等
            if (e.Row.RowType == DataControlRowType.DataRow)
            {
                // 設定每個欄位的寬度沒設定會套用css的預設寬度
                e.Row.Cells[0].Style.Add("width", "80px");
                e.Row.Cells[1].Style.Add("width", "200px");
                e.Row.Cells[2].Style.Add("width", "100px");
                e.Row.Cells[3].Style.Add("width", "100px");
                e.Row.Cells[4].Style.Add("width", "100px");
                e.Row.Cells[5].Style.Add("width", "100px");
                //e.Row.Cells[6].Style.Add("width", "80px");
                //e.Row.Cells[7].Style.Add("width", "50px");
                //e.Row.Cells[8].Style.Add("width", "50px");
                //e.Row.Cells[9].Style.Add("width", "60px");
                //e.Row.Cells[10].Style.Add("width", "130px");
                //e.Row.Cells[11].Style.Add("width", "80px");
                //e.Row.Cells[12].Style.Add("width", "80px");
                //e.Row.Cells[13].Style.Add("width", "80px");
                //e.Row.Cells[14].Style.Add("width", "50px");

                // 設定內容置中
                for (int i = 0; i < e.Row.Cells.Count; i++)
                {
                    e.Row.Cells[i].HorizontalAlign = HorizontalAlign.Center;
                }

            }
        }
    }

    // 添加缺少的 NewList1 方法（分頁處理）
    protected void NewList1(object sender, GridViewPageEventArgs e)
    {
        GridView1.PageIndex = e.NewPageIndex;
        GetGV1Data1(); // 重新綁定資料
    }

    // 添加缺少的 ExportToExcel 方法
    protected void ExportToExcel(object sender, EventArgs e)
    {
        try
        {
            using (var workbook = new XLWorkbook())
            {
                var worksheet = workbook.Worksheets.Add("統計報表");

                worksheet.Cell("A1").Value = ViewState["chartTitle"].ToString();
                worksheet.Cell("A1").Style.Font.Bold = true;
                worksheet.Cell("A1").Style.Font.FontSize = 16;

                Response.Clear();
                Response.Buffer = true;
                Response.Charset = "";
                Response.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                Response.AddHeader("content-disposition", "attachment;filename=Report.xlsx");

                using (MemoryStream MyMemoryStream = new MemoryStream())
                {
                    workbook.SaveAs(MyMemoryStream);
                    MyMemoryStream.WriteTo(Response.OutputStream);
                    Response.Flush();
                    Response.End();
                }
            }
        }
        catch (Exception ex)
        {
            msg.Text = "Excel 匯出錯誤: " + ex.Message;
        }
    }

    // 添加缺少的 ExportToPdf 方法
    protected void ExportToPdf(object sender, EventArgs e)
    {
        try
        {
            Response.ContentType = "application/pdf";
            Response.AddHeader("content-disposition", "attachment;filename=Report.pdf");
            Response.Cache.SetCacheability(HttpCacheability.NoCache);

            StringWriter sw = new StringWriter();
            HtmlTextWriter hw = new HtmlTextWriter(sw);

            P1.RenderControl(hw);
            string htmlContent = sw.ToString();

            Document pdfDoc = new Document(PageSize.A4, 10f, 10f, 10f, 0f);
            PdfWriter.GetInstance(pdfDoc, Response.OutputStream);
            pdfDoc.Open();

            // 添加中文字體支持
            string fontPath = Server.MapPath("~/fonts/kaiu.ttf");
            BaseFont bf = BaseFont.CreateFont(fontPath, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
            iTextSharp.text.Font font = new iTextSharp.text.Font(bf, 12);

            pdfDoc.Add(new Paragraph(ViewState["chartTitle"].ToString(), font));
            pdfDoc.Close();
            Response.Write(pdfDoc);
            Response.End();
        }
        catch (Exception ex)
        {
            msg.Text = "PDF 匯出錯誤: " + ex.Message;
        }
    }

    // 添加簡單的測試按鈕方法
    protected void Button_test_Click(object sender, EventArgs e)
    {
        msg.Text = "<p style='color: green; font-weight: bold;'>✅ 測試按鈕工作正常！時間: " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "</p>";

        // 測試資料庫連線
        try
        {
            if (db.conn.State != ConnectionState.Open)
            {
                db.conn.Open();
            }
            msg.Text += "<p style='color: green;'>✅ 資料庫連線成功</p>";

            // 測試簡單查詢
            SqlCommand testCmd = new SqlCommand("SELECT COUNT(*) FROM [school].[dbo].[V_recowish1]", db.conn);
            int count = (int)testCmd.ExecuteScalar();
            msg.Text += $"<p style='color: green;'>✅ V_recowish1 視圖有 {count} 筆資料</p>";

            db.conn.Close();
        }
        catch (Exception ex)
        {
            msg.Text += $"<p style='color: red;'>❌ 資料庫測試失敗: {ex.Message}</p>";
        }
    }

    private void GetGV1Data1()
    {
        string chartTitle = string.Empty;
        string queryTypeText = string.Empty;
        List<string> actualColumns = new List<string>();
        string actualViewName = ""; // 移到方法開始處，擴大作用域
        string sqlStr = ""; // 添加 sqlStr 變數，擴大作用域

        // 確定查詢類型文字
        switch (RadioButtonList2.SelectedValue)
        {
            case "1":
                queryTypeText = "實名制人數分布";
                break;
            case "2":
                queryTypeText = "報到人數分布";
                break;
            case "3":
                queryTypeText = "繳交畢業證書人數分布";
                break;
            case "4":
                queryTypeText = "繳費人數分布";
                break;
            default:
                queryTypeText = "實名制人數分布";
                break;
        }

        try
        {
            ScriptManager.RegisterStartupScript(this, GetType(), "debugStart", "addDebugInfo('🔧 GetGV1Data1 開始執行');", true);

            if (db.conn.State != ConnectionState.Open)
            {
                db.conn.Open();
                ScriptManager.RegisterStartupScript(this, GetType(), "debugConn", "addDebugInfo('📡 資料庫連線已開啟');", true);
            }

            SqlCommand cmd = new SqlCommand("", db.conn);

            // 先查詢視圖的實際欄位名稱
            if (RadioButtonList1.SelectedValue == "1")
            {
                actualViewName = "V_recowish1";
                chartTitle = "日間部" + queryTypeText + " - " + DateTime.Now.ToString("yyyy/MM/dd");
                ViewState["chartTitle"] = "日間部科系" + queryTypeText;
            }
            else if (RadioButtonList1.SelectedValue == "2")
            {
                actualViewName = "V_recowish3";
                chartTitle = "碩士班" + queryTypeText + " - " + DateTime.Now.ToString("yyyy/MM/dd");
                ViewState["chartTitle"] = "碩士班系所" + queryTypeText;
            }
            else if (RadioButtonList1.SelectedValue == "3")
            {
                actualViewName = "V_recowish5";
                chartTitle = "進修部" + queryTypeText + " - " + DateTime.Now.ToString("yyyy/MM/dd");
                ViewState["chartTitle"] = "進修部科系" + queryTypeText;
            }

            ScriptManager.RegisterStartupScript(this, GetType(), "debugView", $"addDebugInfo('📊 使用視圖: {actualViewName}');", true);

            if (string.IsNullOrEmpty(actualViewName))
            {
                msg.Text = "請選擇分析項目";
                P1.Visible = false;
                ScriptManager.RegisterStartupScript(this, GetType(), "hideLoadingError", "hideLoading(); addDebugInfo('❌ 視圖名稱為空');", true);
                return;
            }

            // 使用 SELECT * 避免編碼問題，然後通過索引存取欄位
            sqlStr = $"SELECT * FROM [school].[dbo].[{actualViewName}]";

            // 根據診斷結果，我們知道欄位順序是固定的：
            // 0: 學制, 1: 科系, 2: 實名制人數, 3: 報到人數, 4: 繳交畢業證書人數, 5: 繳費人數

            // 確定數據欄位索引
            int dataColumnIndex = 2; // 預設為第3個欄位（索引2）
            switch (RadioButtonList2.SelectedValue)
            {
                case "1":
                    dataColumnIndex = 2; // 實名制人數 - 第3個欄位
                    break;
                case "2":
                    dataColumnIndex = 3; // 報到人數 - 第4個欄位
                    break;
                case "3":
                    dataColumnIndex = 4; // 繳交畢業證書人數 - 第5個欄位
                    break;
                case "4":
                    dataColumnIndex = 5; // 繳費人數 - 第6個欄位
                    break;
                default:
                    dataColumnIndex = 2;
                    break;
            }

            // 執行查詢
            ScriptManager.RegisterStartupScript(this, GetType(), "debugSQL", $"addDebugInfo('🔍 執行 SQL: {sqlStr}');", true);

            SqlDataAdapter da = new SqlDataAdapter(sqlStr, db.conn);
            DataTable GV1 = new DataTable();
            da.Fill(GV1);

            ScriptManager.RegisterStartupScript(this, GetType(), "debugResult", $"addDebugInfo('📊 查詢結果: {GV1.Rows.Count} 筆資料');", true);

            // 保存之前的調試資訊
            string previousDebugInfo = msg.Text;

            // 直接在 msg 中顯示調試資訊
            msg.Text += $"<p>📊 查詢結果: {GV1.Rows.Count} 筆資料</p>";

            // 顯示資料表結構資訊
            if (GV1.Rows.Count > 0)
            {
                msg.Text += $"<p>📋 資料表欄位數量: {GV1.Columns.Count}</p>";
                msg.Text += "<p>📋 欄位名稱: ";
                for (int i = 0; i < GV1.Columns.Count; i++)
                {
                    msg.Text += $"[{i}]{GV1.Columns[i].ColumnName} ";
                }
                msg.Text += "</p>";
            }

            if (GV1.Rows.Count > 0)
            {
                // 綁定 GridView
                GridView1.DataSource = GV1;
                GridView1.DataBind();

                ScriptManager.RegisterStartupScript(this, GetType(), "debugGrid", "addDebugInfo('📋 GridView 資料綁定完成');", true);

                // 準備圖表數據
                List<object[]> chartData = new List<object[]>();
                int totalCount = 0;
                int maxValue = 0;
                string maxDepartment = "";

                int validDataCount = 0;
                ScriptManager.RegisterStartupScript(this, GetType(), "debugProcess", $"addDebugInfo('🔄 開始處理資料，使用欄位索引: {dataColumnIndex}');", true);

                // 直接在 msg 中顯示調試資訊
                msg.Text += $"<p>🔄 開始處理資料，使用欄位索引: {dataColumnIndex}</p>";

                try
                {
                    msg.Text += $"<p>🔍 開始檢查每一筆資料...</p>";

                    foreach (DataRow row in GV1.Rows)
                    {
                        try
                        {
                            // 使用欄位索引存取資料 (索引1是科系)
                            string departmentName = row[1].ToString();
                            int dataValue = 0;

                            if (int.TryParse(row[dataColumnIndex].ToString(), out dataValue))
                            {
                                chartData.Add(new object[] { departmentName, dataValue });
                                validDataCount++;

                                if (validDataCount <= 3) // 只顯示前3筆的詳細資訊
                                {
                                    msg.Text += $"<p>✅ 第{validDataCount}筆: {departmentName} = {dataValue}</p>";
                                }
                            }
                            else
                            {
                                msg.Text += $"<p>⚠️ 無法解析數值: {row[dataColumnIndex]} (科系: {departmentName})</p>";
                            }
                        }
                        catch (Exception rowEx)
                        {
                            msg.Text += $"<p style='color: red;'>❌ 處理資料列錯誤: {rowEx.Message}</p>";
                        }
                    }

                    ScriptManager.RegisterStartupScript(this, GetType(), "debugProcessResult", $"addDebugInfo('📊 資料處理完成，有效資料筆數: {validDataCount}');", true);
                    msg.Text += $"<p>📊 資料處理完成，有效資料筆數: {validDataCount}</p>";
                }
                catch (Exception dataEx)
                {
                    ScriptManager.RegisterStartupScript(this, GetType(), "debugDataError", $"addDebugInfo('❌ 資料處理錯誤: {dataEx.Message}');", true);
                    msg.Text += $"<p style='color: red;'>❌ 資料處理錯誤: {dataEx.Message}</p>";
                    throw; // 重新拋出錯誤讓外層 catch 處理
                }

                // 計算統計數據
                foreach (DataRow row in GV1.Rows)
                {
                    int currentValue = 0;
                    if (int.TryParse(row[dataColumnIndex].ToString(), out currentValue))
                    {
                        totalCount += currentValue;
                        if (currentValue > maxValue)
                        {
                            maxValue = currentValue;
                            maxDepartment = row[1].ToString(); // 索引1是科系
                        }
                    }
                }

                if (validDataCount > 0)
                {
                    // 儲存統計資料到 ViewState 供前端顯示
                    ViewState["DepartmentCount"] = validDataCount;
                    ViewState["TotalCount"] = totalCount;
                    ViewState["MaxValue"] = maxValue;
                    ViewState["MaxDepartment"] = maxDepartment;

                    // 生成圖表
                    var serializer = new JavaScriptSerializer();

                    // 修正：為圖表資料添加標題行
                    var chartDataWithHeader = new List<object[]>();
                    chartDataWithHeader.Add(new object[] { "科系", queryTypeText.Replace("分布", "") });
                    chartDataWithHeader.AddRange(chartData);

                    string jsonData = serializer.Serialize(chartDataWithHeader);

                    string script = $@"
                        // 設置全域變數
                        window.chartData = {jsonData};
                        window.chartTitle = '{chartTitle}';

                        // 添加調試資訊
                        addDebugInfo('📊 圖表資料已設置: ' + window.chartData.length + ' 筆');
                        addDebugInfo('📈 圖表標題: ' + window.chartTitle);

                        // 初始化圖表
                        setTimeout(function() {{
                            try {{
                                initializeChart();
                                addDebugInfo('✅ 圖表初始化完成');
                            }} catch(e) {{
                                addDebugInfo('❌ 圖表初始化錯誤: ' + e.message);
                                console.error('圖表初始化錯誤:', e);
                            }}
                        }}, 500);

                        // 隱藏載入動畫並顯示動畫效果
                        hideLoading();
                        animateStatCards();
                        animateTableRows();
                    ";

                    // 顯示統計信息，保留之前的調試資訊
                    msg.Text = previousDebugInfo + $@"
                        <div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0; border: 1px solid #c3e6cb;'>
                            <h4 style='color: #155724; margin-top: 0;'>✅ 查詢成功</h4>
                            <p style='color: #155724; font-size: 0.9em;'>視圖: {actualViewName}</p>
                            <p style='color: #155724; font-size: 0.9em;'>查詢類型: {queryTypeText}</p>
                            <p style='color: #155724; font-size: 0.9em;'>資料欄位索引: {dataColumnIndex}</p>
                            <div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 10px;'>
                                <div style='background: white; padding: 10px; border-radius: 5px; border-left: 4px solid #007bff;'>
                                    <strong>總計</strong><br/>
                                    <span style='font-size: 1.2em; color: #007bff;'>{totalCount}</span>
                                </div>
                                <div style='background: white; padding: 10px; border-radius: 5px; border-left: 4px solid #28a745;'>
                                    <strong>最高科系</strong><br/>
                                    <span style='font-size: 1.1em; color: #28a745;'>{maxDepartment}</span><br/>
                                    <span style='color: #666;'>({maxValue}人)</span>
                                </div>
                                <div style='background: white; padding: 10px; border-radius: 5px; border-left: 4px solid #17a2b8;'>
                                    <strong>科系數量</strong><br/>
                                    <span style='font-size: 1.2em; color: #17a2b8;'>{validDataCount}</span>
                                </div>
                            </div>
                        </div>
                    ";

                    P1.Visible = true;
                    ScriptManager.RegisterStartupScript(this, GetType(), "debugSuccess", "addDebugInfo('✅ 資料處理完成，顯示結果面板');", true);
                    ScriptManager.RegisterStartupScript(this, GetType(), "drawChart", script, true);
                }
                else
                {
                    msg.Text = "查無有效的數據！";
                    P1.Visible = false;
                    ScriptManager.RegisterStartupScript(this, GetType(), "hideLoadingNoData", "hideLoading(); addDebugInfo('❌ 查無有效數據');", true);
                }
            }
            else
            {
                msg.Text = "查無資料！SQL: " + sqlStr;
                P1.Visible = false;
                GridView1.DataSource = null;
                GridView1.DataBind();
                ScriptManager.RegisterStartupScript(this, GetType(), "hideLoadingNoData", "hideLoading(); addDebugInfo('❌ 查無資料');", true);
            }

            db.conn.Close();
        }
        catch (Exception ex)
        {
            // 在調試面板中顯示錯誤
            ScriptManager.RegisterStartupScript(this, GetType(), "debugError", $"addDebugInfo('❌ 系統錯誤: {ex.Message.Replace("'", "\\'")}');", true);

            // 增強錯誤訊息，提供更多調試資訊
            string errorDetails = $@"
                <div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0; border: 1px solid #f5c6cb;'>
                    <h4 style='color: #721c24; margin-top: 0;'>❌ 查詢錯誤</h4>
                    <p><strong>錯誤訊息:</strong> {ex.Message}</p>
                    <p><strong>視圖名稱:</strong> {(string.IsNullOrEmpty(actualViewName) ? "未選擇" : actualViewName)}</p>
                    <p><strong>使用的查詢:</strong> {sqlStr}</p>
                    <p><strong>選擇的分析項目:</strong> {RadioButtonList1.SelectedValue ?? "未選擇"}</p>
                    <p><strong>選擇的查詢類型:</strong> {RadioButtonList2.SelectedValue ?? "未選擇"}</p>
                    <details style='margin-top: 10px;'>
                        <summary style='cursor: pointer; color: #721c24;'>詳細錯誤資訊</summary>
                        <pre style='background: white; padding: 10px; border-radius: 5px; margin-top: 5px; font-size: 12px; overflow-x: auto;'>{ex.ToString()}</pre>
                    </details>
                </div>
            ";

            msg.Text = errorDetails;
            P1.Visible = false;
            GridView1.DataSource = null;
            GridView1.DataBind();
            ScriptManager.RegisterStartupScript(this, GetType(), "hideLoadingError", "hideLoading();", true);
        }
    }

    protected void ExportPDF_Click(object sender, EventArgs e)
    {
        try
        {
            Response.ContentType = "application/pdf";
            Response.AddHeader("content-disposition", "attachment;filename=Report.pdf");
            Response.Cache.SetCacheability(HttpCacheability.NoCache);

            StringWriter sw = new StringWriter();
            HtmlTextWriter hw = new HtmlTextWriter(sw);

            P1.RenderControl(hw);
            string htmlContent = sw.ToString();

            Document pdfDoc = new Document(PageSize.A4, 10f, 10f, 10f, 0f);
            PdfWriter.GetInstance(pdfDoc, Response.OutputStream);
            pdfDoc.Open();

            // 添加中文字體支持
            string fontPath = Server.MapPath("~/fonts/kaiu.ttf");
            BaseFont bf = BaseFont.CreateFont(fontPath, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
            iTextSharp.text.Font font = new iTextSharp.text.Font(bf, 12);

            pdfDoc.Add(new Paragraph(ViewState["chartTitle"].ToString(), font));
            pdfDoc.Close();
            Response.Write(pdfDoc);
            Response.End();
        }
        catch (Exception ex)
        {
            msg.Text = "PDF 匯出錯誤: " + ex.Message;
        }
    }

    protected void ExportExcel_Click(object sender, EventArgs e)
    {
        try
        {
            using (var workbook = new XLWorkbook())
            {
                var worksheet = workbook.Worksheets.Add("統計報表");

                worksheet.Cell("A1").Value = ViewState["chartTitle"].ToString();
                worksheet.Cell("A1").Style.Font.Bold = true;
                worksheet.Cell("A1").Style.Font.FontSize = 16;

                Response.Clear();
                Response.Buffer = true;
                Response.Charset = "";
                Response.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                Response.AddHeader("content-disposition", "attachment;filename=Report.xlsx");

                using (MemoryStream MyMemoryStream = new MemoryStream())
                {
                    workbook.SaveAs(MyMemoryStream);
                    MyMemoryStream.WriteTo(Response.OutputStream);
                    Response.Flush();
                    Response.End();
                }
            }
        }
        catch (Exception ex)
        {
            msg.Text = "Excel 匯出錯誤: " + ex.Message;
        }
    }

    public bool CheckUserSession()
    {
        if (Session["tech_no"] == null)
        {
            ScriptManager.RegisterStartupScript(this.Page, this.Page.GetType(), "Msg", "alert('作業逾時或尚未登入,請登入後再繼續作業!');location.href='/Logout.aspx';", true);
            return false;
        }
        else
        {
            return true;
        }
    }
}
