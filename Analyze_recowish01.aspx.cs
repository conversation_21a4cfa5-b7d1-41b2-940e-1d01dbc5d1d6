﻿using ClosedXML.Excel;
using iTextSharp.text;
using iTextSharp.text.pdf;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Drawing.Printing;
using System.IO;
using System.Web;
using System.Web.Script.Serialization;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Xml.Linq;

public partial class Analyze_recowish01 : System.Web.UI.Page
{
    dbconnection db = new dbconnection();
    App_Func appfun = new App_Func();

    protected void Page_Load(object sender, EventArgs e)
    {
        if (CheckUserSession() == false) { return; }
        if (!IsPostBack)
        {
            // 初始化頁面
        }
    }

    // 添加缺少的 RadioButtonList1_SelectedIndexChanged 方法
    protected void RadioButtonList1_SelectedIndexChanged(object sender, EventArgs e)
    {
        // 當選擇變更時，可以在這裡處理邏輯
        // 目前保持空白，或者可以加入一些 UI 更新邏輯
    }

    // 添加缺少的 RadioButtonList2_SelectedIndexChanged 方法
    protected void RadioButtonList2_SelectedIndexChanged(object sender, EventArgs e)
    {
        // 當查詢項目類型選擇變更時，可以在這裡處理邏輯
        // 目前保持空白，或者可以加入一些 UI 更新邏輯
    }

    //protected void Button1_Click(object sender, EventArgs e)
    //{
    //    // 顯示載入動畫
    //    ScriptManager.RegisterStartupScript(this, GetType(), "showLoading", "showLoading();", true);

    //    // 重置消息和圖表區域
    //    msg.Text = "";
    //    P1.Visible = false;

    //    try
    //    {
    //        GetGV1Data1();
    //    }
    //    catch (Exception ex)
    //    {
    //        msg.Text = "查詢時發生錯誤: " + ex.Message;
    //        ScriptManager.RegisterStartupScript(this, GetType(), "hideLoadingError", "hideLoading();", true);
    //    }
    //}

    // 添加缺少的 Button_search_Click 方法（與 Button1_Click 功能相同）
    protected void Button_search_Click(object sender, EventArgs e)
    {
        // 顯示載入動畫
        ScriptManager.RegisterStartupScript(this, GetType(), "showLoading", "showLoading();", true);

        // 重置消息和圖表區域
        msg.Text = "";
        P1.Visible = false;

        try
        {
            GetGV1Data1();
            P1.Visible = true;
        }
        catch (Exception ex)
        {
            msg.Text = "查詢時發生錯誤: " + ex.Message;
            ScriptManager.RegisterStartupScript(this, GetType(), "hideLoadingError", "hideLoading();", true);
        }
    }

    // 添加缺少的 GridView1_RowDataBound 方法
    protected void GridView1_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            // 可以在這裡添加行資料綁定的自定義邏輯
            // 例如：格式化數據、添加樣式等
            if (e.Row.RowType == DataControlRowType.DataRow)
            {
                // 設定每個欄位的寬度沒設定會套用css的預設寬度
                e.Row.Cells[0].Style.Add("width", "80px");
                e.Row.Cells[1].Style.Add("width", "200px");
                e.Row.Cells[2].Style.Add("width", "100px");
                e.Row.Cells[3].Style.Add("width", "100px");
                e.Row.Cells[4].Style.Add("width", "100px");
                e.Row.Cells[5].Style.Add("width", "100px");
                //e.Row.Cells[6].Style.Add("width", "80px");
                //e.Row.Cells[7].Style.Add("width", "50px");
                //e.Row.Cells[8].Style.Add("width", "50px");
                //e.Row.Cells[9].Style.Add("width", "60px");
                //e.Row.Cells[10].Style.Add("width", "130px");
                //e.Row.Cells[11].Style.Add("width", "80px");
                //e.Row.Cells[12].Style.Add("width", "80px");
                //e.Row.Cells[13].Style.Add("width", "80px");
                //e.Row.Cells[14].Style.Add("width", "50px");

                // 設定內容置中
                for (int i = 0; i < e.Row.Cells.Count; i++)
                {
                    e.Row.Cells[i].HorizontalAlign = HorizontalAlign.Center;
                }

            }
        }
    }

    // 添加缺少的 NewList1 方法（分頁處理）
    protected void NewList1(object sender, GridViewPageEventArgs e)
    {
        GridView1.PageIndex = e.NewPageIndex;
        GetGV1Data1(); // 重新綁定資料
    }

    // 添加缺少的 ExportToExcel 方法
    protected void ExportToExcel(object sender, EventArgs e)
    {
        try
        {
            using (var workbook = new XLWorkbook())
            {
                var worksheet = workbook.Worksheets.Add("統計報表");

                worksheet.Cell("A1").Value = ViewState["chartTitle"].ToString();
                worksheet.Cell("A1").Style.Font.Bold = true;
                worksheet.Cell("A1").Style.Font.FontSize = 16;

                Response.Clear();
                Response.Buffer = true;
                Response.Charset = "";
                Response.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                Response.AddHeader("content-disposition", "attachment;filename=Report.xlsx");

                using (MemoryStream MyMemoryStream = new MemoryStream())
                {
                    workbook.SaveAs(MyMemoryStream);
                    MyMemoryStream.WriteTo(Response.OutputStream);
                    Response.Flush();
                    Response.End();
                }
            }
        }
        catch (Exception ex)
        {
            msg.Text = "Excel 匯出錯誤: " + ex.Message;
        }
    }

    // 添加缺少的 ExportToPdf 方法
    protected void ExportToPdf(object sender, EventArgs e)
    {
        try
        {
            Response.ContentType = "application/pdf";
            Response.AddHeader("content-disposition", "attachment;filename=Report.pdf");
            Response.Cache.SetCacheability(HttpCacheability.NoCache);

            StringWriter sw = new StringWriter();
            HtmlTextWriter hw = new HtmlTextWriter(sw);

            P1.RenderControl(hw);
            string htmlContent = sw.ToString();

            Document pdfDoc = new Document(PageSize.A4, 10f, 10f, 10f, 0f);
            PdfWriter.GetInstance(pdfDoc, Response.OutputStream);
            pdfDoc.Open();

            // 添加中文字體支持
            string fontPath = Server.MapPath("~/fonts/kaiu.ttf");
            BaseFont bf = BaseFont.CreateFont(fontPath, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
            iTextSharp.text.Font font = new iTextSharp.text.Font(bf, 12);

            pdfDoc.Add(new Paragraph(ViewState["chartTitle"].ToString(), font));
            pdfDoc.Close();
            Response.Write(pdfDoc);
            Response.End();
        }
        catch (Exception ex)
        {
            msg.Text = "PDF 匯出錯誤: " + ex.Message;
        }
    }

    private void GetGV1Data1()
    {
        string chartTitle = string.Empty;
        string queryTypeText = string.Empty;
        List<string> actualColumns = new List<string>();

        // 確定查詢類型文字
        switch (RadioButtonList2.SelectedValue)
        {
            case "1":
                queryTypeText = "實名制人數分布";
                break;
            case "2":
                queryTypeText = "報到人數分布";
                break;
            case "3":
                queryTypeText = "繳交畢業證書人數分布";
                break;
            case "4":
                queryTypeText = "繳費人數分布";
                break;
            default:
                queryTypeText = "實名制人數分布";
                break;
        }

        try
        {
            if (db.conn.State != ConnectionState.Open)
            {
                db.conn.Open();
            }

            SqlCommand cmd = new SqlCommand("", db.conn);

            // 先查詢視圖的實際欄位名稱
            string actualViewName = "";
            if (RadioButtonList1.SelectedValue == "1")
            {
                actualViewName = "V_recowish1";
                chartTitle = "日間部" + queryTypeText + " - " + DateTime.Now.ToString("yyyy/MM/dd");
                ViewState["chartTitle"] = "日間部科系" + queryTypeText;
            }
            else if (RadioButtonList1.SelectedValue == "2")
            {
                actualViewName = "V_recowish3";
                chartTitle = "碩士班" + queryTypeText + " - " + DateTime.Now.ToString("yyyy/MM/dd");
                ViewState["chartTitle"] = "碩士班系所" + queryTypeText;
            }
            else if (RadioButtonList1.SelectedValue == "3")
            {
                actualViewName = "V_recowish5";
                chartTitle = "進修部" + queryTypeText + " - " + DateTime.Now.ToString("yyyy/MM/dd");
                ViewState["chartTitle"] = "進修部科系" + queryTypeText;
            }

            if (string.IsNullOrEmpty(actualViewName))
            {
                msg.Text = "請選擇分析項目";
                P1.Visible = false;
                ScriptManager.RegisterStartupScript(this, GetType(), "hideLoadingError", "hideLoading();", true);
                return;
            }

            // 查詢實際的欄位名稱
            cmd.CommandText = $"SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '{actualViewName}' ORDER BY ORDINAL_POSITION";
            SqlDataReader reader = cmd.ExecuteReader();
            while (reader.Read())
            {
                actualColumns.Add(reader["COLUMN_NAME"].ToString());
            }
            reader.Close();

            if (actualColumns.Count < 6)
            {
                msg.Text = "錯誤: 視圖結構異常，欄位數量不足。找到的欄位: " + string.Join(", ", actualColumns);
                P1.Visible = false;
                ScriptManager.RegisterStartupScript(this, GetType(), "hideLoadingError", "hideLoading();", true);
                return;
            }

            // 使用實際的欄位名稱構建 SQL
            string sqlStr = $"SELECT '學制','科系','實名制人數','報到人數','繳交畢業證書人數','繳費人數' FROM [school].[dbo].[{actualViewName}]";

            // 確定數據欄位名稱
            string dataColumnName = "";
            switch (RadioButtonList2.SelectedValue)
            {
                case "1":
                    dataColumnName = "📊 實名制人數分布"; // 第3個欄位通常是實名制人數
                    break;
                case "2":
                    dataColumnName = "✅ 報到人數分布"; // 第4個欄位通常是報到人數
                    break;
                case "3":
                    dataColumnName = "📋 繳交畢業證書人數分布"; // 第5個欄位通常是繳交畢業證書人數
                    break;
                case "4":
                    dataColumnName = "💰 繳費人數分布"; // 第6個欄位通常是繳費人數
                    break;
                default:
                    dataColumnName = "";
                    break;
            }

            // 執行查詢
            SqlDataAdapter da = new SqlDataAdapter(sqlStr, db.conn);
            DataTable GV1 = new DataTable();
            da.Fill(GV1);

            if (GV1.Rows.Count > 0)
            {
                // 綁定 GridView
                GridView1.DataSource = GV1;
                GridView1.DataBind();

                // 準備圖表數據
                List<object[]> chartData = new List<object[]>();
                int totalCount = 0;
                int maxValue = 0;
                string maxDepartment = "";

                int validDataCount = 0;
                foreach (DataRow row in GV1.Rows)
                {
                    // 使用實際的欄位名稱 (第2個欄位通常是科系)
                    string departmentName = row["科系"[1]].ToString();
                    int dataValue = 0;

                    if (int.TryParse(row[dataColumnName].ToString(), out dataValue))
                    {
                        chartData.Add(new object[] { departmentName, dataValue });
                        validDataCount++;
                    }
                }

                // 計算統計數據
                foreach (DataRow row in GV1.Rows)
                {
                    int currentValue = 0;
                    if (int.TryParse(row[dataColumnName].ToString(), out currentValue))
                    {
                        totalCount += currentValue;
                        if (currentValue > maxValue)
                        {
                            maxValue = currentValue;
                            maxDepartment = row["科系"[1]].ToString();
                        }
                    }
                }

                if (validDataCount > 0)
                {
                    // 生成圖表
                    var serializer = new JavaScriptSerializer();
                    string jsonData = serializer.Serialize(chartData);

                    string script = $@"
                        google.charts.load('current', {{'packages':['corechart']}});
                        google.charts.setOnLoadCallback(drawChart);
                        
                        function drawChart() {{
                            var data = new google.visualization.DataTable();
                            data.addColumn('string', '科系');
                            data.addColumn('number', '{queryTypeText.Replace("分布", "")}');
                            data.addRows({jsonData});

                            var options = {{
                                title: '{chartTitle}',
                                titleTextStyle: {{
                                    fontSize: 16,
                                    bold: true
                                }},
                                pieHole: 0.4,
                                backgroundColor: 'transparent',
                                legend: {{
                                    position: 'right',
                                    textStyle: {{fontSize: 12}}
                                }},
                                chartArea: {{
                                    left: 20,
                                    top: 60,
                                    width: '70%',
                                    height: '80%'
                                }}
                            }};

                            var chart = new google.visualization.PieChart(document.getElementById('chart_div'));
                            chart.draw(data, options);
                        }}
                        hideLoading();
                    ";

                    // 顯示統計信息
                    msg.Text = $@"
                        <div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>
                            <h4 style='color: #333; margin-top: 0;'>📊 統計摘要</h4>
                            <p style='color: #666; font-size: 0.9em;'>使用欄位: {string.Join(", ", actualColumns)}</p>
                            <p style='color: #666; font-size: 0.9em;'>數據欄位: {dataColumnName}</p>
                            <div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;'>
                                <div style='background: white; padding: 10px; border-radius: 5px; border-left: 4px solid #007bff;'>
                                    <strong>總計</strong><br/>
                                    <span style='font-size: 1.2em; color: #007bff;'>{totalCount}</span>
                                </div>
                                <div style='background: white; padding: 10px; border-radius: 5px; border-left: 4px solid #28a745;'>
                                    <strong>最高科系</strong><br/>
                                    <span style='font-size: 1.1em; color: #28a745;'>{maxDepartment}</span><br/>
                                    <span style='color: #666;'>({maxValue}人)</span>
                                </div>
                                <div style='background: white; padding: 10px; border-radius: 5px; border-left: 4px solid #17a2b8;'>
                                    <strong>科系數量</strong><br/>
                                    <span style='font-size: 1.2em; color: #17a2b8;'>{validDataCount}</span>
                                </div>
                            </div>
                        </div>
                    ";

                    P1.Visible = true;
                    ScriptManager.RegisterStartupScript(this, GetType(), "drawChart", script, true);
                }
                else
                {
                    msg.Text = "查無有效的數據！";
                    P1.Visible = false;
                    ScriptManager.RegisterStartupScript(this, GetType(), "hideLoadingNoData", "hideLoading();", true);
                }
            }
            else
            {
                msg.Text = "查無資料！SQL: " + sqlStr;
                P1.Visible = false;
                GridView1.DataSource = null;
                GridView1.DataBind();
                ScriptManager.RegisterStartupScript(this, GetType(), "hideLoadingNoData", "hideLoading();", true);
            }

            db.conn.Close();
        }
        catch (Exception ex)
        {
            msg.Text = "查詢錯誤: " + ex.Message + "<br/>欄位資訊: " + string.Join(", ", actualColumns);
            P1.Visible = false;
            GridView1.DataSource = null;
            GridView1.DataBind();
            ScriptManager.RegisterStartupScript(this, GetType(), "hideLoadingError", "hideLoading();", true);
        }
    }

    protected void ExportPDF_Click(object sender, EventArgs e)
    {
        try
        {
            Response.ContentType = "application/pdf";
            Response.AddHeader("content-disposition", "attachment;filename=Report.pdf");
            Response.Cache.SetCacheability(HttpCacheability.NoCache);

            StringWriter sw = new StringWriter();
            HtmlTextWriter hw = new HtmlTextWriter(sw);

            P1.RenderControl(hw);
            string htmlContent = sw.ToString();

            Document pdfDoc = new Document(PageSize.A4, 10f, 10f, 10f, 0f);
            PdfWriter.GetInstance(pdfDoc, Response.OutputStream);
            pdfDoc.Open();

            // 添加中文字體支持
            string fontPath = Server.MapPath("~/fonts/kaiu.ttf");
            BaseFont bf = BaseFont.CreateFont(fontPath, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
            iTextSharp.text.Font font = new iTextSharp.text.Font(bf, 12);

            pdfDoc.Add(new Paragraph(ViewState["chartTitle"].ToString(), font));
            pdfDoc.Close();
            Response.Write(pdfDoc);
            Response.End();
        }
        catch (Exception ex)
        {
            msg.Text = "PDF 匯出錯誤: " + ex.Message;
        }
    }

    protected void ExportExcel_Click(object sender, EventArgs e)
    {
        try
        {
            using (var workbook = new XLWorkbook())
            {
                var worksheet = workbook.Worksheets.Add("統計報表");

                worksheet.Cell("A1").Value = ViewState["chartTitle"].ToString();
                worksheet.Cell("A1").Style.Font.Bold = true;
                worksheet.Cell("A1").Style.Font.FontSize = 16;

                Response.Clear();
                Response.Buffer = true;
                Response.Charset = "";
                Response.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                Response.AddHeader("content-disposition", "attachment;filename=Report.xlsx");

                using (MemoryStream MyMemoryStream = new MemoryStream())
                {
                    workbook.SaveAs(MyMemoryStream);
                    MyMemoryStream.WriteTo(Response.OutputStream);
                    Response.Flush();
                    Response.End();
                }
            }
        }
        catch (Exception ex)
        {
            msg.Text = "Excel 匯出錯誤: " + ex.Message;
        }
    }

    public bool CheckUserSession()
    {
        if (Session["tech_no"] == null)
        {
            ScriptManager.RegisterStartupScript(this.Page, this.Page.GetType(), "Msg", "alert('作業逾時或尚未登入,請登入後再繼續作業!');location.href='/Logout.aspx';", true);
            return false;
        }
        else
        {
            return true;
        }
    }
}
