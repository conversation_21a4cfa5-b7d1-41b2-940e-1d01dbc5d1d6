<%@ Page Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeFile="Analyze_recowish01.aspx.cs" Inherits="Analyze_recowish01" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

    <html>

    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <title>人數分析</title>
        <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
        <script type="text/javascript">
            google.charts.load('current', { 'packages': ['corechart'] });

            // 初始化圖表函數 - 修正版本
            function initializeChart() {
                addDebugInfo('🔧 initializeChart 被調用');

                // 檢查 Google Charts 是否載入
                if (typeof google === 'undefined') {
                    addDebugInfo('❌ Google Charts 未載入，等待載入...');
                    setTimeout(initializeChart, 500); // 500ms 後重試
                    return;
                }

                // 檢查全域變數
                if (typeof window.chartData !== 'undefined' && typeof window.chartTitle !== 'undefined') {
                    addDebugInfo('✅ 找到圖表資料，資料筆數: ' + window.chartData.length);
                    addDebugInfo('📊 圖表標題: ' + window.chartTitle);

                    // 直接調用 drawChart，不需要再次設置回調
                    drawChart();
                } else {
                    addDebugInfo('❌ 圖表資料未找到: chartData=' + (typeof window.chartData) + ', chartTitle=' + (typeof window.chartTitle));

                    // 顯示錯誤訊息在圖表區域
                    var chartDiv = document.getElementById('piechart');
                    if (chartDiv) {
                        chartDiv.innerHTML = '<div style="color: #999; text-align: center; padding: 50px; font-size: 16px;">📊 等待圖表資料載入...</div>';
                    }
                }
            }

            function drawChart() {
                addDebugInfo('🎨 drawChart 被調用');

                // 使用全域變數
                var chartData = window.chartData;
                var chartTitle = window.chartTitle;

                // 再次檢查是否有 chartData 和 chartTitle
                if (typeof chartData === 'undefined' || typeof chartTitle === 'undefined') {
                    addDebugInfo('❌ 圖表資料尚未載入，無法繪製圖表');
                    var chartDiv = document.getElementById('piechart');
                    if (chartDiv) {
                        chartDiv.innerHTML = '<div style="color: red; text-align: center; padding: 50px; font-size: 16px;">❌ 圖表資料載入失敗</div>';
                    }
                    return;
                }

                if (!chartData || chartData.length <= 1) {
                    addDebugInfo('❌ 圖表資料不足: ' + (chartData ? chartData.length : 0) + ' 筆');
                    var chartDiv = document.getElementById('piechart');
                    if (chartDiv) {
                        chartDiv.innerHTML = '<div style="color: #999; text-align: center; padding: 50px; font-size: 16px;">📊 暫無圖表資料</div>';
                    }
                    return;
                }

                addDebugInfo('📊 開始繪製圖表，資料筆數: ' + chartData.length);

                try {
                    // 檢查圖表容器是否存在
                    var chartContainer = document.getElementById("piechart");
                    if (!chartContainer) {
                        addDebugInfo('❌ 找不到圖表容器 #piechart');
                        return;
                    }

                    // 清空容器內容
                    chartContainer.innerHTML = '';

                    var data = google.visualization.arrayToDataTable(chartData);

                    var options = {
                        title: chartTitle,
                        pieHole: 0.3,
                        width: '100%',
                        height: 400,
                        titleTextStyle: {
                            fontSize: 16,
                            fontName: '微軟正黑體',
                            bold: true,
                            color: '#333'
                        },
                        legend: {
                            position: 'right',
                            alignment: 'center',
                            textStyle: {
                                fontSize: 11,
                                fontName: '微軟正黑體'
                            }
                        },
                        tooltip: {
                            textStyle: {
                                fontSize: 12,
                                fontName: '微軟正黑體'
                            }
                        },
                        chartArea: {
                            left: 20,
                            top: 50,
                            width: '70%',
                            height: '80%'
                        },
                        backgroundColor: 'transparent',
                        colors: ['#667eea', '#764ba2', '#4facfe', '#00f2fe', '#52c41a', '#73d13d', '#ff4d4f', '#ff7875', '#faad14', '#ffc53d']
                    };

                    var chart = new google.visualization.PieChart(chartContainer);
                    chart.draw(data, options);

                    addDebugInfo('✅ 圖表繪製完成');

                    setTimeout(function () {
                        try {
                            var chartDiv = document.getElementById("piechart");
                            var svg = chartDiv.getElementsByTagName("svg")[0];
                            var canvas = document.createElement("canvas");
                            canvas.width = svg.width.baseVal.value;
                            canvas.height = svg.height.baseVal.value;

                            var xml = new XMLSerializer().serializeToString(svg);
                            var img = new Image();
                            img.src =
                                "data:image/svg+xml;base64," +
                                btoa(unescape(encodeURIComponent(xml)));

                            img.onload = function () {
                                var ctx = canvas.getContext("2d");
                                ctx.drawImage(img, 0, 0);
                                var pngData = canvas.toDataURL("image/png");
                                document.getElementById(
                                    "<%= hiddenChartData.ClientID %>"
                                ).value = pngData;
                                console.log("圖表已轉換為 Base64");
                            };
                        } catch (error) {
                            console.error("轉換圖表時發生錯誤:", error);
                        }
                    }, 1000);
                } catch (error) {
                    console.error("繪製圖表時發生錯誤:", error);
                    // 隱藏圖表容器，顯示錯誤訊息
                    var chartContainer = document.getElementById("piechart");
                    if (chartContainer) {
                        chartContainer.innerHTML = '<div style="text-align:center; padding:50px; color:#999;">圖表載入失敗</div>';
                    }
                }
            }

            // 添加查詢類型變更提示功能
            function updateQueryTypeHint() {
                var queryType = document.querySelector('input[name$="RadioButtonList2"]:checked');
                var analysisType = document.querySelector('input[name$="RadioButtonList1"]:checked');

                if (queryType && analysisType) {
                    var msg = document.getElementById('<%= msg.ClientID %>');
                    var queryTypeText = queryType.nextSibling.textContent || queryType.nextElementSibling.textContent;
                    var analysisTypeText = analysisType.nextSibling.textContent || analysisType.nextElementSibling.textContent;
                    msg.innerHTML = '📊 目前選擇：' + analysisTypeText + ' - ' + queryTypeText;
                    msg.style.color = '#1890ff';
                }
            }            // 添加載入效果與視覺回饋
            function showLoading() {
                var btn = document.getElementById('<%= Button_search.ClientID %>');
                btn.innerHTML = '<span class="spinner">⏳</span> 查詢中...';
                btn.disabled = true;
                btn.style.background = 'linear-gradient(135deg, #d9d9d9 0%, #bfbfbf 100%)';

                // 移除任何現有的載入遮罩，避免堆疊
                var existingOverlay = document.getElementById('loadingOverlay');
                if (existingOverlay && existingOverlay.parentNode) {
                    existingOverlay.parentNode.removeChild(existingOverlay);
                }

                // 添加載入中覆蓋層
                //var overlay = document.createElement('div');
                //overlay.id = 'loadingOverlay';
                //overlay.className = 'loading-overlay';
                //overlay.innerHTML = '<div class="loading-spinner"></div><div class="loading-text">資料查詢中，請稍候...</div>';

                // 添加創建時間標記，用於安全檢查
                //overlay.setAttribute('data-creation-time', new Date().getTime().toString());

                //document.querySelector('.analysis-panel').appendChild(overlay);

                // 暫時隱藏舊的查詢結果
                //var resultPanel = document.getElementById('<%= P1.ClientID %>');
                //if (resultPanel && !resultPanel.classList.contains('loading')) {
                //    resultPanel.classList.add('loading');
                //    resultPanel.style.opacity = '0.5';
                //    resultPanel.style.pointerEvents = 'none';
                //}

                // 安全機制：即使出錯，也確保最多 10 秒後自動移除載入遮罩
                //window.loadingTimeout = setTimeout(function () {
                //    console.log('安全機制觸發：自動移除載入遮罩');
                //    hideLoading();
                //}, 10000);
            }

            function hideLoading() {
                var btn = document.getElementById('<%= Button_search.ClientID %>');
                if (btn) { // 確保按鈕存在
                    btn.innerHTML = '🔍 開始查詢';
                    btn.disabled = false;
                    btn.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                }

                // 清除安全超時計時器
                if (window.loadingTimeout) {
                    clearTimeout(window.loadingTimeout);
                    window.loadingTimeout = null;
                }

                // 移除載入中覆蓋層 - 更可靠的方法
                var overlay = document.getElementById('loadingOverlay');
                if (overlay) {
                    overlay.classList.add('fade-out');
                    setTimeout(function () {
                        try {
                            if (overlay.parentNode) {
                                overlay.parentNode.removeChild(overlay);
                            }
                        } catch (e) {
                            console.log('移除遮罩時發生錯誤:', e);
                            // 強制方式清除所有可能的遮罩
                            document.querySelectorAll('.loading-overlay').forEach(function (el) {
                                if (el.parentNode) {
                                    el.parentNode.removeChild(el);
                                }
                            });
                        }
                    }, 500);
                } else {
                    // 作為備案，檢查是否有任何具有 loading-overlay 類的元素
                    document.querySelectorAll('.loading-overlay').forEach(function (el) {
                        el.classList.add('fade-out');
                        setTimeout(function () {
                            if (el.parentNode) {
                                el.parentNode.removeChild(el);
                            }
                        }, 500);
                    });
                }

                // 恢復查詢結果的顯示
                var resultPanel = document.getElementById('<%= P1.ClientID %>');
                if (resultPanel && resultPanel.classList.contains('loading')) {
                    resultPanel.classList.remove('loading');
                    resultPanel.style.opacity = '1';
                    resultPanel.style.pointerEvents = 'auto';

                    // 添加淡入效果
                    resultPanel.classList.add('fade-in');
                    setTimeout(function () {
                        resultPanel.classList.remove('fade-in');
                    }, 1000);
                }
            }            // CSS animation for loading spinner and visual effects
            var style = document.createElement('style');
            style.textContent = `
                @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
                .spinner { display: inline-block; animation: spin 1s linear infinite; }

                .loading-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(255, 255, 255, 0.9);
                    z-index: 9999; /* 確保在最上層 */
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    transition: opacity 0.5s;
                    backdrop-filter: blur(3px); /* 添加模糊效果增強可見度 */
                }

                .fade-out {
                    opacity: 0;
                }

                .fade-in {
                    animation: fadeIn 1s;
                }

                @keyframes fadeIn {
                    from { opacity: 0.5; }
                    to { opacity: 1; }
                }

                .loading-spinner {
                    width: 50px;
                    height: 50px;
                    border: 5px solid #f3f3f3;
                    border-top: 5px solid #667eea;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    box-shadow: 0 0 10px rgba(0,0,0,0.1); /* 添加陰影增強可見度 */
                }

                .loading-text {
                    margin-top: 20px;
                    font-family: '微軟正黑體 Light';
                    font-size: 16px;
                    font-weight: bold;
                    color: #667eea;
                    text-shadow: 0 0 5px white; /* 文字陰影增強可見度 */
                }

                /* 數據加載動畫 */
                .stat-card .number {
                    position: relative;
                    overflow: hidden;
                }

                .stat-card .number.animate {
                    animation: countUp 1.5s ease-out forwards;
                }

                @keyframes countUp {
                    from { opacity: 0; transform: translateY(20px); }
                    to { opacity: 1; transform: translateY(0); }
                }

                /* 圖表載入效果 */
                .chart-loading {
                    height: 400px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
                    border-radius: 10px;
                    margin-bottom: 20px;
                }            `;
            document.head.appendChild(style);

            // 調試面板功能
            function toggleDebug() {
                var panel = document.getElementById('debugPanel');
                var btn = document.getElementById('showDebugBtn');

                if (panel.style.display === 'none' || panel.style.display === '') {
                    panel.style.display = 'block';
                    btn.style.display = 'none';
                } else {
                    panel.style.display = 'none';
                    btn.style.display = 'inline-block';
                }
            }

            // 添加調試資訊
            function addDebugInfo(message) {
                var debugContent = document.getElementById('debugContent');
                if (debugContent) {
                    var timestamp = new Date().toLocaleTimeString();
                    debugContent.innerHTML += '<br/>[' + timestamp + '] ' + message;
                    debugContent.scrollTop = debugContent.scrollHeight;
                }
                console.log('[DEBUG] ' + message);
            }

            // 全局錯誤處理器
            window.addEventListener('error', function (event) {
                addDebugInfo('❌ JavaScript錯誤: ' + event.message + ' (行: ' + event.lineno + ')');
                return false;
            });            // 更新統計數值的動畫效果
            function animateStatCards() {
                addDebugInfo('🎬 開始執行統計卡片動畫');
                var statNumbers = document.querySelectorAll('.stat-card .number');
                statNumbers.forEach(function (numberElement) {
                    numberElement.classList.add('animate');
                });
            }

            // 添加表格行滑動效果
            function animateTableRows() {
                addDebugInfo('📊 開始執行表格行動畫');
                var gridView = document.getElementById('<%= GridView1.ClientID %>');
                if (gridView) {
                    var rows = gridView.querySelectorAll('tr');
                    rows.forEach(function (row, index) {
                        setTimeout(function () {
                            row.style.opacity = '0';
                            row.style.transform = 'translateX(-20px)';
                            row.style.transition = 'opacity 0.5s, transform 0.5s';

                            setTimeout(function () {
                                row.style.opacity = '1';
                                row.style.transform = 'translateX(0)';
                            }, 50);
                        }, index * 50);
                    });
                }
            }

            // 頁面載入完成後綁定事件
            document.addEventListener('DOMContentLoaded', function () {
                addDebugInfo('🚀 頁面載入完成，開始初始化...');

                // 檢查 Google Charts 是否載入
                if (typeof google !== 'undefined') {
                    addDebugInfo('✅ Google Charts 已載入');
                } else {
                    addDebugInfo('❌ Google Charts 未載入');
                }

                // 檢查圖表容器是否存在
                var chartContainer = document.getElementById('piechart');
                if (chartContainer) {
                    addDebugInfo('✅ 圖表容器 #piechart 已找到');
                } else {
                    addDebugInfo('❌ 圖表容器 #piechart 未找到');
                }

                // 確保查詢按鈕處於正確狀態
                var searchBtn = document.getElementById('<%= Button_search.ClientID %>');
                if (searchBtn) {
                    searchBtn.disabled = false;
                    searchBtn.innerHTML = '🔍 開始查詢';
                    searchBtn.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';

                    // 移除前端事件監聽器，讓 ASP.NET PostBack 正常工作
                    // 只添加調試資訊，不攔截點擊事件
                    addDebugInfo('🔧 查詢按鈕已準備就緒，等待 ASP.NET PostBack');
                }

                // 添加測試按鈕的事件監聽
                var testBtn = document.getElementById('<%= Button_test.ClientID %>');
                if (testBtn) {
                    testBtn.addEventListener('click', function (e) {
                        addDebugInfo('🧪 測試按鈕被點擊');
                    });
                }

                addDebugInfo('🎯 初始化完成，系統準備就緒');
            }

                var radioButtons = document.querySelectorAll('input[type="radio"]');
            radioButtons.forEach(function (radio) {
                radio.addEventListener('change', updateQueryTypeHint);
            });

            // 初始動畫效果
            var resultPanel = document.getElementById('<%= P1.ClientID %>');
            if (resultPanel && resultPanel.style.display !== 'none') {
                animateStatCards();
                animateTableRows();

                // 打開頁面時，如果結果已顯示，添加淡入效果
                resultPanel.style.opacity = '0';
                resultPanel.style.transition = 'opacity 1s';
                setTimeout(function () {
                    resultPanel.style.opacity = '1';
                }, 200);
            }

            // 綁定匯出按鈕效果
            var exportButtons = document.querySelectorAll('.btn-export-excel, .btn-export-pdf');
            exportButtons.forEach(function (btn) {
                btn.addEventListener('click', function () {
                    var originalText = this.innerText;
                    this.innerHTML = '<span class="spinner">⏳</span> 處理中...';
                    this.disabled = true;

                    // 5秒後恢復按鈕，以防止卡住
                    setTimeout(function () {
                        btn.innerHTML = originalText;
                        btn.disabled = false;
                    }, 5000);
                });
            });
            });

            // 測試圖表函數
            function testChart() {
                addDebugInfo('🧪 測試圖表按鈕被點擊');

                // 創建測試資料
                window.chartData = [
                    ['科系', '人數'],
                    ['資訊工程系', 150],
                    ['電機工程系', 120],
                    ['機械工程系', 100],
                    ['化學工程系', 80],
                    ['土木工程系', 90]
                ];
                window.chartTitle = '測試圖表 - 各科系人數分布';

                addDebugInfo('📊 測試資料已設置');
                addDebugInfo('📈 資料內容: ' + JSON.stringify(window.chartData));

                // 調用圖表初始化
                initializeChart();
            }

            // 移除重複的圖表函數，使用頁面頂部的版本
        </script>
        <style>
            /* 全域樣式，確保載入效果工作正常 */
            body {
                margin: 0;
                padding: 0;
                position: relative;
                min-height: 100vh;
            }

            /* 保證 loading-overlay 始終在最上層 */
            .loading-overlay {
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                width: 100% !important;
                height: 100% !important;
                background-color: rgba(255, 255, 255, 0.9) !important;
                z-index: 9999 !important;
                /* 確保在最上層 */
                display: flex !important;
                flex-direction: column !important;
                justify-content: center !important;
                align-items: center !important;
                transition: opacity 0.5s !important;
                backdrop-filter: blur(3px) !important;
                /* 添加模糊效果增強可見度 */
                pointer-events: all !important;
                /* 確保可以接收點擊事件 */
            }

            /* 美化按鈕樣式 */
            .btn-modern {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border: none;
                color: white;
                padding: 12px 24px;
                border-radius: 25px;
                font-size: 14px;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s ease;
                box-shadow: 0 4px 15px 0 rgba(102, 126, 234, 0.3);
                margin: 5px;
                min-width: 120px;
            }

            .btn-modern:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px 0 rgba(102, 126, 234, 0.5);
                background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            }

            .btn-modern:active {
                transform: translateY(0);
                box-shadow: 0 4px 15px 0 rgba(102, 126, 234, 0.3);
            }

            .btn-modern:disabled {
                opacity: 0.7;
                cursor: not-allowed;
                transform: none;
                box-shadow: none;
                background: linear-gradient(135deg, #d9d9d9 0%, #bfbfbf 100%);
            }

            /* 美化查詢按鈕 */
            .btn-query {
                background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                border: none;
                color: white;
                padding: 10px 20px;
                border-radius: 20px;
                font-size: 13px;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s ease;
                box-shadow: 0 3px 10px 0 rgba(79, 172, 254, 0.3);
                margin: 3px;
                min-width: 100px;
            }

            .btn-query:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 20px 0 rgba(79, 172, 254, 0.5);
                background: linear-gradient(135deg, #43a7fe 0%, #00d4ff 100%);
            }

            /* 美化匯出按鈕 */
            .btn-export-excel {
                background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
                border: none;
                color: white;
                padding: 10px 20px;
                border-radius: 20px;
                font-size: 13px;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s ease;
                box-shadow: 0 3px 10px 0 rgba(82, 196, 26, 0.3);
                margin: 3px;
            }

            .btn-export-excel:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 20px 0 rgba(82, 196, 26, 0.5);
            }

            .btn-export-pdf {
                background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
                border: none;
                color: white;
                padding: 10px 20px;
                border-radius: 20px;
                font-size: 13px;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s ease;
                box-shadow: 0 3px 10px 0 rgba(255, 77, 79, 0.3);
                margin: 3px;
            }

            .btn-export-pdf:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 20px 0 rgba(255, 77, 79, 0.5);
            }

            /* 美化表格樣式 */
            .gridview-table {
                border-collapse: collapse;
                width: 100%;
                background: white;
                border-radius: 10px;
                overflow: hidden;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
                margin: 20px 0;
            }

            .gridview-table td {
                width: auto;
                height: auto;
                text-align: center;
                border: none;
                padding: 12px;
                font-size: 14px;
                border-bottom: 1px solid #f0f0f0;
            }

            .gridview-table th {
                border: none;
                padding: 15px 12px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                font-weight: bold;
                text-align: center;
            }

            .gridview-table tr:hover {
                background-color: #f8f9ff;
                transition: background-color 0.3s ease;
            }

            /* 美化面板樣式 */
            .analysis-panel {
                background: white;
                border-radius: 15px;
                padding: 25px;
                margin: 20px 0;
                box-shadow: 0 6px 30px rgba(0, 0, 0, 0.1);
            }

            /* 美化選項區域 */
            .option-section {
                background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
                border-radius: 15px;
                padding: 20px;
                margin: 15px 0;
                border: 1px solid #e6e8ff;
            }

            /* 美化圖表區域 */
            .chart-container {
                background: white;
                border-radius: 15px;
                padding: 20px;
                margin: 20px 0;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
                text-align: center;
            }

            /* RadioButton 美化 */
            .RadioButtonList1 {
                display: flex;
                flex-wrap: wrap;
                gap: 15px;
                margin: 15px 0;
            }

            .RadioButtonList1 input[type="radio"] {
                margin-right: 8px;
                transform: scale(1.2);
                accent-color: #667eea;
            }

            .RadioButtonList1 label {
                display: flex;
                align-items: center;
                margin: 0;
                padding: 8px 15px;
                font-weight: 500;
                color: #333;
                background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
                border: 2px solid #e6e8ff;
                border-radius: 20px;
                cursor: pointer;
                transition: all 0.3s ease;
                min-width: 140px;
                justify-content: center;
            }

            .RadioButtonList1 label:hover {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                transform: translateY(-2px);
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            }

            .RadioButtonList1 input[type="radio"]:checked+label,
            .RadioButtonList1 label:has(input[type="radio"]:checked) {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border-color: #667eea;
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            }

            /* 新增訊息樣式 */
            .msg-info {
                background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
                border: 1px solid #91d5ff;
                border-radius: 10px;
                padding: 12px 20px;
                margin: 15px 0;
                font-size: 14px;
                font-weight: 500;
            }

            /* 數據統計卡片樣式 */
            .stats-summary {
                display: flex;
                justify-content: space-around;
                margin: 20px 0;
                flex-wrap: wrap;
                gap: 15px;
            }

            .stat-card {
                background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
                border: 2px solid #e6e8ff;
                border-radius: 15px;
                padding: 20px;
                text-align: center;
                min-width: 150px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
                transition: all 0.3s ease;
            }

            .stat-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 8px 30px rgba(102, 126, 234, 0.2);
                border-color: #667eea;
            }

            .stat-card .number {
                font-size: 28px;
                font-weight: bold;
                color: #667eea;
                margin-bottom: 8px;
            }

            .stat-card .label {
                font-size: 14px;
                color: #666;
                font-weight: 500;
            }

            /* 兩欄布局樣式 */
            .results-layout {
                display: flex;
                gap: 20px;
                margin: 20px 0;
                flex-wrap: wrap;
            }

            .chart-section {
                flex: 1;
                min-width: 400px;
            }

            .table-section {
                flex: 1;
                min-width: 400px;
            }

            /* 響應式設計 */
            @media (max-width: 1024px) {
                .results-layout {
                    flex-direction: column;
                }

                .chart-section,
                .table-section {
                    min-width: 100%;
                }
            }

            /* 匯出按鈕區域 */
            .export-section {
                margin-top: 30px;
                padding: 20px;
                background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
                border-radius: 15px;
                border: 1px solid #e6e8ff;
            }
        </style>
    </head>

    <body>
        <div class="analysis-panel">
            <asp:Table ID="tblselect" runat="server" BorderWidth="0px" Width="100%" CssClass="option-section">
                <asp:TableRow ID="TableRow1" runat="server">
                    <asp:TableCell ID="TableCell1" runat="server" Width="100%">
                        <asp:Label ID="label3" Font-Bold="true" Height="26px" runat="server" Font-Names="微軟正黑體 Light" Font-Size="16" Text="📊 人數分析項目:" ForeColor="#333"></asp:Label><br /><br />
                        <asp:RadioButtonList ID="RadioButtonList1" runat="server" AutoPostBack="True" OnSelectedIndexChanged="RadioButtonList1_SelectedIndexChanged" RepeatLayout="Flow"
                            RepeatDirection="Horizontal" CssClass="RadioButtonList1">
                            <asp:ListItem Value="1">日間部人數分析</asp:ListItem>
                            <asp:ListItem Value="2">碩士班人數分析</asp:ListItem>
                            <asp:ListItem Value="3">進修部人數分析</asp:ListItem>
                        </asp:RadioButtonList>
                    </asp:TableCell>
                </asp:TableRow>

                <asp:TableRow ID="TableRow4" runat="server">
                    <asp:TableCell ID="TableCell4" runat="server" Width="100%">
                        <asp:Label ID="label4" Font-Bold="true" Height="26px" runat="server" Font-Names="微軟正黑體 Light" Font-Size="16" Text="📈 查詢項目類型:" ForeColor="#333"></asp:Label><br /><br />
                        <asp:RadioButtonList ID="RadioButtonList2" runat="server" RepeatLayout="Flow" RepeatDirection="Horizontal" CssClass="RadioButtonList1" AutoPostBack="True"
                            OnSelectedIndexChanged="RadioButtonList2_SelectedIndexChanged">
                            <asp:ListItem Value="1" Selected="True">📊 實名制人數分布</asp:ListItem>
                            <asp:ListItem Value="2">✅ 報到人數分布</asp:ListItem>
                            <asp:ListItem Value="3">📋 繳交畢業證書人數分布</asp:ListItem>
                            <asp:ListItem Value="4">💰 繳費人數分布</asp:ListItem>
                        </asp:RadioButtonList>
                    </asp:TableCell>
                </asp:TableRow>

                <asp:TableRow ID="TableRow2" runat="server">
                    <asp:TableCell ID="TableCell2" runat="server" Width="100%">
                        <asp:Button ID="Button_search" runat="server" Text="🔍 開始查詢" OnClick="Button_search_Click" CssClass="btn-modern" />
                        <asp:Button ID="Button_test" runat="server" Text="🧪 測試連線" OnClick="Button_test_Click" CssClass="btn-modern" style="margin-left: 10px;" />
                        <input type="button" value="📝 純HTML測試" onclick="document.getElementById('<%= msg.ClientID %>').innerHTML='HTML按鈕工作正常！';"
                            style="margin-left: 10px; padding: 10px; background: #28a745; color: white; border: none; border-radius: 5px;" />
                        <input type="button" value="📊 測試圖表" onclick="testChart();" style="margin-left: 10px; padding: 10px; background: #ff6b35; color: white; border: none; border-radius: 5px;" />
                    </asp:TableCell>
                </asp:TableRow>
                <asp:TableRow ID="TableRow3" runat="server">
                    <asp:TableCell ID="TableCell3" runat="server" Width="100%">
                        <asp:Label ID="msg" Font-Bold="true" Height="26px" runat="server" ForeColor="#1890ff" Font-Size="14"></asp:Label>
                    </asp:TableCell>
                </asp:TableRow>

                <asp:TableRow ID="TableRowDebug" runat="server">
                    <asp:TableCell ID="TableCellDebug" runat="server" Width="100%">
                        <div id="debugPanel"
                            style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 10px; margin: 10px 0; font-family: monospace; font-size: 12px; display: none;">
                            <strong>🐛 調試資訊:</strong><br />
                            <div id="debugContent">點擊查詢按鈕後將顯示調試資訊...</div>
                            <button type="button" onclick="toggleDebug()"
                                style="margin-top: 10px; padding: 5px 10px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer;">隱藏調試</button>
                        </div>
                        <button type="button" id="showDebugBtn" onclick="toggleDebug()"
                            style="padding: 5px 10px; background: #6c757d; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 12px;">顯示調試資訊</button>
                    </asp:TableCell>
                </asp:TableRow>
            </asp:Table>
            <asp:Panel ID="P1" runat="server" Visible="False">
                <!-- 統計摘要卡片 -->
                <div class="stats-summary">
                    <div class="stat-card">
                        <div class="number"><%= ViewState["DepartmentCount"] ?? "0" %></div>
                        <div class="label">📚 科系數量</div>
                    </div>
                    <div class="stat-card">
                        <div class="number"><%= ViewState["TotalCount"] ?? "0" %></div>
                        <div class="label">👥 總計人數</div>
                    </div>
                    <div class="stat-card">
                        <div class="number"><%= ViewState["MaxValue"] ?? "0" %></div>
                        <div class="label">🏆 最高數量</div>
                    </div>
                    <div class="stat-card">
                        <div class="number" style="font-size: 16px; color: #52c41a;"><%= ViewState["MaxDepartment"] ?? "-" %></div>
                        <div class="label">🥇 領先科系</div>
                    </div>
                </div>

                <!-- 使用兩欄布局 -->
                <div class="results-layout">
                    <!-- 左側：圖表 -->
                    <div class="chart-section">
                        <div class="chart-container">
                            <h3 style="color: #333; margin-bottom: 20px;">📈 圖表分析</h3>
                            <div id="piechart"
                                style="width: 100%; height: 400px; margin: 0 auto; background: #f8f9ff; border-radius: 10px; display: flex; align-items: center; justify-content: center;">
                                <div style="color: #999; font-size: 16px;">圖表載入中...</div>
                            </div>
                        </div>
                    </div>

                    <!-- 右側：資料表格 -->
                    <div class="table-section">
                        <div class="chart-container">
                            <h3 style="color: #333; margin-bottom: 20px;">📊 數據統計表</h3>
                            <div style="max-height: 400px; overflow-y: auto;">
                                <asp:GridView ID="GridView1" runat="server" CellPadding="2" Font-Names="微軟正黑體 Light" ForeColor="Black" HorizontalAlign="Center" OnRowDataBound="GridView1_RowDataBound"
                                    Width="100%" Font-Bold="True" CellSpacing="2" AutoGenerateColumns="true" OnPageIndexChanging="NewList1" CssClass="gridview-table">
                                    <AlternatingRowStyle Font-Names="微軟正黑體 Light" HorizontalAlign="Center" BackColor="#f8f9ff" />
                                    <EditRowStyle Font-Names="微軟正黑體 Light" />
                                    <EmptyDataRowStyle Font-Names="微軟正黑體 Light" />
                                    <FooterStyle BackColor="#f0f2ff" Font-Names="微軟正黑體 Light" />
                                    <HeaderStyle BackColor="#667eea" Font-Bold="True" Font-Names="微軟正黑體 Light" ForeColor="White" HorizontalAlign="Center" />
                                    <PagerStyle BackColor="#f0f2ff" Font-Names="微軟正黑體 Light" ForeColor="#667eea" HorizontalAlign="Center" />
                                    <RowStyle BackColor="White" Font-Names="微軟正黑體 Light" />
                                    <SelectedRowStyle BackColor="#667eea" Font-Bold="True" Font-Names="微軟正黑體 Light" ForeColor="White" />
                                    <SortedAscendingCellStyle BackColor="#f8f9ff" />
                                    <SortedAscendingHeaderStyle BackColor="#5a67d8" />
                                    <SortedDescendingCellStyle BackColor="#f0f2ff" />
                                    <SortedDescendingHeaderStyle BackColor="#4c51bf" />
                                </asp:GridView>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 匯出按鈕區域 -->
                <div class="export-section">
                    <div style="text-align: center; margin-top: 20px;">
                        <asp:Button ID="btnExportExcel" runat="server" CssClass="btn-export-excel" Text="📊 匯出 Excel" OnClick="ExportToExcel" />
                        <asp:Button ID="btnExportPdf" runat="server" CssClass="btn-export-pdf" Text="📄 匯出 PDF" OnClick="ExportToPdf" />
                    </div>
                </div>

                <!-- 📌隱藏欄位用來傳遞 Base64 圖片 -->
                <input type="hidden" id="hiddenChartData" runat="server" />
            </asp:Panel>
        </div>

        <%--<script type="text/javascript">
            // 全域錯誤處理器，確保在發生錯誤時移除載入遮罩
            window.onerror = function (message, source, lineno, colno, error) {
                console.error('發生錯誤: ', message);

                // 嘗試移除所有可能的載入遮罩
                var overlays = document.querySelectorAll('.loading-overlay');
                if (overlays.length > 0) {
                    console.log('錯誤處理器發現', overlays.length, '個載入遮罩需移除');
                    overlays.forEach(function (overlay) {
                        if (overlay && overlay.parentNode) {
                            overlay.parentNode.removeChild(overlay);
                        }
                    });
                }

                // 嘗試恢複所有按鈕狀態
                var searchBtn = document.getElementById('<%= Button_search.ClientID %>');
        if (searchBtn) {
        searchBtn.disabled = false;
        searchBtn.innerHTML = '🔍 開始查詢';
        searchBtn.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
        }

        // 不中斷默認錯誤處理
        return false;
        };

        // 添加備用清除機制，定期檢查是否有殘留的載入遮罩
        setInterval(function () {
        var overlays = document.querySelectorAll('.loading-overlay');
        if (overlays.length > 0) {
        // 如果載入遮罩存在超過 15 秒，則強制移除
        // 檢查是否有遮罩的創建時間
        overlays.forEach(function (overlay) {
        var creationTime = parseInt(overlay.getAttribute('data-creation-time') || '0');
        var currentTime = new Date().getTime();

        // 如果遮罩存在超過 15 秒或無法確定其創建時間，則強制移除
        if (creationTime === 0 || (currentTime - creationTime > 10000)) {
        console.log('發現長時間存在的載入遮罩，強制移除');
        if (overlay.parentNode) {
        overlay.parentNode.removeChild(overlay);
        }

        // 恢複按鈕狀態
        var searchBtn = document.getElementById('<%= Button_search.ClientID %>');
        if (searchBtn) {
        searchBtn.disabled = false;
        searchBtn.innerHTML = '🔍 開始查詢';
        searchBtn.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
        }
        }
        });
        }
        }, 5000); // 每 5 秒檢查一次
        </script>--%>
    </body>

    </html>
</asp:Content>