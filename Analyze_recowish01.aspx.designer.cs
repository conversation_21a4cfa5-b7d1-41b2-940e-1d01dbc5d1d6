﻿//------------------------------------------------------------------------------
// <auto-generated>
//     這段程式碼是由工具產生的。
//
//     變更這個檔案可能會導致不正確的行為，而且如果已重新產生
//     程式碼，變更將會遺失。
// </auto-generated>
//------------------------------------------------------------------------------

public partial class Analyze_recowish01
{

    /// <summary>
    /// form1 控制項。
    /// </summary>
    /// <remarks>
    /// 自動產生的欄位。
    /// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
    /// </remarks>
    protected global::System.Web.UI.HtmlControls.HtmlForm form1;

    /// <summary>
    /// tblselect 控制項。
    /// </summary>
    /// <remarks>
    /// 自動產生的欄位。
    /// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Table tblselect;

    /// <summary>
    /// label3 控制項。
    /// </summary>
    /// <remarks>
    /// 自動產生的欄位。
    /// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Label label3;    /// <summary>
                                                                 /// RadioButtonList1 控制項。
                                                                 /// </summary>
                                                                 /// <remarks>
                                                                 /// 自動產生的欄位。
                                                                 /// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
                                                                 /// </remarks>
    protected global::System.Web.UI.WebControls.RadioButtonList RadioButtonList1;

    /// <summary>
    /// TableRow4 控制項。
    /// </summary>
    /// <remarks>
    /// 自動產生的欄位。
    /// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
    /// </remarks>
    protected global::System.Web.UI.WebControls.TableRow TableRow4;

    /// <summary>
    /// TableCell4 控制項。
    /// </summary>
    /// <remarks>
    /// 自動產生的欄位。
    /// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
    /// </remarks>
    protected global::System.Web.UI.WebControls.TableCell TableCell4;

    /// <summary>
    /// label4 控制項。
    /// </summary>
    /// <remarks>
    /// 自動產生的欄位。
    /// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Label label4;

    /// <summary>
    /// RadioButtonList2 控制項。
    /// </summary>
    /// <remarks>
    /// 自動產生的欄位。
    /// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
    /// </remarks>
    protected global::System.Web.UI.WebControls.RadioButtonList RadioButtonList2;

    /// <summary>
    /// Button_search 控制項。
    /// </summary>
    /// <remarks>
    /// 自動產生的欄位。
    /// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Button Button_search;

    /// <summary>
    /// msg 控制項。
    /// </summary>
    /// <remarks>
    /// 自動產生的欄位。
    /// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Label msg;

    /// <summary>
    /// P1 控制項。
    /// </summary>
    /// <remarks>
    /// 自動產生的欄位。
    /// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Panel P1;

    /// <summary>
    /// GridView1 控制項。
    /// </summary>
    /// <remarks>
    /// 自動產生的欄位。
    /// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
    /// </remarks>
    protected global::System.Web.UI.WebControls.GridView GridView1;

    /// <summary>
    /// btnExportExcel 控制項。
    /// </summary>
    /// <remarks>
    /// 自動產生的欄位。
    /// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
    /// </remarks>
    protected global::System.Web.UI.WebControls.Button btnExportExcel;

    /// <summary>
    /// btnExportPdf 控制項。
    /// </summary>
    /// <remarks>
    /// 自動產生的欄位。
    /// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
    /// </remarks>        protected global::System.Web.UI.WebControls.Button btnExportPdf;

    /// <summary>
    /// hiddenChartData 控制項。
    /// </summary>
    /// <remarks>
    /// 自動產生的欄位。
    /// 若要修改，請將欄位宣告從設計工具檔案移至程式碼後置檔案。
    /// </remarks>
    protected global::System.Web.UI.HtmlControls.HtmlInputHidden hiddenChartData;
}
